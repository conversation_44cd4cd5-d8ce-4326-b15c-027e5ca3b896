/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-nested-ternary */
/* eslint-disable complexity */
/* eslint-disable react/require-default-props */
import {
  Group,
  Text,
  ActionIcon,
  Box,
  Badge,
  UnstyledButton,
  useMantineTheme,
  useMantineColorScheme,
  Tooltip,
} from '@mantine/core';
import {
  IconCheck,
  IconAlertCircle,
  IconInfoCircle,
  IconCircleCheck,
} from '@tabler/icons-react';
import { useIsClient } from '../../hooks/useIsClient';
import type { Notification } from '../../requests/notifications';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead?: (id: string) => void;
  onClick?: () => void;
  compact?: boolean;
}

const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'SUCCESS':
      return IconCircleCheck;
    case 'WARNING':
      return IconAlertCircle;
    case 'ERROR':
      return IconAlertCircle;
    case 'INFO':
    default:
      return IconInfoCircle;
  }
};

const getNotificationColor = (type: string) => {
  switch (type) {
    case 'SUCCESS':
      return 'green';
    case 'WARNING':
      return 'orange';
    case 'ERROR':
      return 'red';
    case 'INFO':
    default:
      return 'blue';
  }
};

export default function NotificationItem({
  notification,
  onMarkAsRead,
  onClick,
  compact = false,
}: NotificationItemProps) {
  const { t } = useTranslation('notifications');
  const isClient = useIsClient();
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === 'dark';

  // Function to get notification content - prefer backend content over translations
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const getTranslatedContent = (notification: any) => {
    // Debug logging to help identify the issue
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('Notification data:', {
        id: notification.id,
        type: notification.notification_type,
        title: notification.title,
        message: notification.message,
        metadata: notification.metadata,
      });
    }

    // Always use the original title and message from the backend if they exist
    // The backend should be responsible for sending properly localized content
    // based on user language preferences
    if (notification.title && notification.message) {
      return {
        title: notification.title,
        message: notification.message,
      };
    }

    // Fallback to translations only if backend doesn't provide title/message
    const typeKey = notification.notification_type?.toLowerCase() || 'default';
    const titleKey = `types.${typeKey}.title`;
    const messageKey = `types.${typeKey}.message`;

    // Try to get translated content, fallback to generic message if translation doesn't exist
    const translatedTitle = t(titleKey);
    const translatedMessage = t(messageKey);

    // Check if translation exists (next-translate returns the key if translation doesn't exist)
    const hasTranslatedTitle = translatedTitle && translatedTitle !== titleKey;
    const hasTranslatedMessage = translatedMessage && translatedMessage !== messageKey;

    return {
      title: hasTranslatedTitle ? translatedTitle : t('types.default.title'),
      message: hasTranslatedMessage ? translatedMessage : t('types.default.message'),
    };
  };

  const { title, message } = getTranslatedContent(notification);

  const IconComponent = getNotificationIcon(notification.notification_type);
  const iconColor = getNotificationColor(notification.notification_type);

  const formatDate = (dateString: string) => {
    if (!isClient) return 'Loading...';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'Invalid date';
    }
  };

  const handleClick = () => {
    if (!notification.read && onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
    if (onClick) {
      onClick();
    }
  };

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
  };

  return (
    <UnstyledButton
      onClick={handleClick}
      style={{
        width: '100%',
        padding: compact ? theme.spacing.xs : theme.spacing.md,
        backgroundColor: notification.read
          ? 'transparent'
          : isDark
            ? theme.colors.dark[6]
            : theme.colors.gray[0],
        borderBottom: `1px solid ${isDark ? theme.colors.dark[4] : theme.colors.gray[2]}`,
        transition: 'background-color 0.2s ease',
      }}
      __vars={{
        '--button-hover': isDark ? theme.colors.dark[5] : theme.colors.gray[1],
      }}
    >
      <Group gap="sm" align="flex-start" wrap="nowrap">
        <IconComponent
          size={compact ? '1rem' : '1.2rem'}
          color={theme.colors[iconColor][6]}
          style={{ marginTop: '2px', flexShrink: 0 }}
        />

        <Box style={{ flex: 1, minWidth: 0 }}>
          <Group gap="xs" justify="space-between" align="flex-start" wrap="nowrap">
            <Box style={{ flex: 1, minWidth: 0 }}>
              <Text
                fw={notification.read ? 400 : 600}
                size={compact ? 'xs' : 'sm'}
                lineClamp={compact ? 1 : 2}
                c={isDark ? 'gray.1' : 'dark.7'}
              >
                {title}
              </Text>
              {!compact && message && (
                <Text
                  size="xs"
                  c="dimmed"
                  lineClamp={2}
                  mt={2}
                >
                  {message}
                </Text>
              )}
            </Box>

            <Group gap="xs" align="center" style={{ flexShrink: 0 }}>
              {!notification.read && (
                <Badge
                  size="xs"
                  variant="filled"
                  color="blue"
                  style={{ flexShrink: 0 }}
                >
                  {t('new')}
                </Badge>
              )}
              {!notification.read && onMarkAsRead && (
                <Tooltip label={t('markAsRead')} position="top">
                  <ActionIcon
                    size="md"
                    variant="subtle"
                    color="gray"
                    onClick={handleMarkAsRead}
                    aria-label={t('markAsRead')}
                    style={{ padding: '4px' }}
                  >
                    <IconCheck size="1rem" />
                  </ActionIcon>
                </Tooltip>
              )}
            </Group>
          </Group>

          <Text
            size="xs"
            c="dimmed"
            mt={compact ? 2 : 4}
          >
            {formatDate(notification.created_at)}
          </Text>
        </Box>
      </Group>
    </UnstyledButton>
  );
}
